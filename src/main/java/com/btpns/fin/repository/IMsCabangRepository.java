package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsCabang;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IMsCabangRepository extends JpaRepository<MsCabang,String> {
    @Query(value = "SELECT * FROM MsCabang mc with(nolock) where mc.cabangId = :cabangId", nativeQuery = true)
    public List<MsCabang> getMsCabangById(@Param("cabangId") String cabangId);

    @Query(value = "SELECT * FROM MsCabang mc with(nolock) ORDER BY mc.CabangId ASC " +
            "OFFSET :#{#pageable.offset} ROWS FETCH NEXT :#{#pageable.pageSize} ROWS ONLY",
           countQuery = "SELECT COUNT(*) FROM MsCabang with(nolock)",
           nativeQuery = true)
    Page<MsCabang> findAllBranchDataPageable(@Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM MsCabang mc with(nolock) where mc.cabangId LIKE %:searchData%", nativeQuery = true)
    Page<MsCabang> findAllByBranchIdPageable(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsCabang mc with(nolock) where mc.CabangDesc LIKE %:searchData%", nativeQuery = true)
    Page<MsCabang> findAllByBranchNamePageable(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsCabang mc with(nolock) where mc.cabangId = :branchId", nativeQuery = true)
    MsCabang findByBranchId(String branchId);

    @Modifying
    @Query(value = "DELETE FROM MsCabang WHERE CabangId = :branchId", nativeQuery = true)
    int deleteBranchData(String branchId);
}
