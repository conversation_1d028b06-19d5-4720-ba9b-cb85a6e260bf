package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsFAQTema;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface IMsFAQTemaRepository extends JpaRepository<MsFAQTema, BigInteger> {
    @Query(value = "SELECT * FROM MsFAQTema WITH(NOLOCK) WHERE visible = '1' ORDER BY ContentOrder ASC", nativeQuery = true)
    Page<MsFAQTema> getListMsFAQTemaPageable(Pageable pageable);

    @Query(value = "SELECT * FROM MsFAQTema WITH(NOLOCK) WHERE FaqId = :faqId", nativeQuery = true)
    MsFAQTema findMsFAQTemaByFaqId(@Param("faqId") String faqId);

    @Query(value = "SELECT FaqId FROM MsFAQTema WITH(NOLOCK) ORDER BY FaqId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastFaqId();

    @Query(value = "SELECT ContentOrder+1 FROM MsFAQTema WITH(NOLOCK) ORDER BY ContentOrder DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    Integer getLastContentOrder();

    @Query(value = "SELECT * FROM MsFAQTema WITH(NOLOCK) WHERE ContentOrder > :contentOrder", nativeQuery = true)
    List<MsFAQTema> getListMsFAQTema(@Param("contentOrder") Integer contentOrder);

    @Modifying
    @Query(value = "DELETE FROM MsFAQTema WHERE FaqId = :faqId", nativeQuery = true)
    int deleteMsFAQTemaByFaqId(@Param("faqId") String faqId);
}
